{"name": "backend", "version": "1.0.0", "description": "a clone of youtube", "type": "module", "main": "index.js", "scripts": {"dev": "nodemon -r dotenv/config --experimental-json-modules src/index.js"}, "author": "v<PERSON><PERSON> kumar", "license": "ISC", "devDependencies": {"nodemon": "^3.1.10"}, "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "mongoose-aggregate-paginate-v2": "^1.1.4", "multer": "^2.0.1"}}